# build the signalsd service image for prod using scratch base.
#
# note that while historically it was necessary to copy ca-certificates and tzdata from the builder to the runtime image, there is no longer a need for this:
# - the time/tzdata package can be used to provide an embedded copy of the timezone database (not currently a requirement for signalsd)
# - CA root certs are included in the app via the x/crypto/x509 package, which means they are versioned and can be updated via e.g depandabot 
# (singalsd does need these certs as it makes outbound https connections when validating github hostesd schemas) 

FROM golang:1.24-alpine AS builder

WORKDIR /app

COPY app/go.mod .
COPY app/go.sum .

RUN go mod download

COPY app .

RUN mkdir runtime

# placeholder args for version info
ARG VERSION=dev
ARG BUILD_DATE=unknown
ARG GIT_COMMIT=unknown

# Build static binary 
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags "-s -w -extldflags '-static' -X github.com/information-sharing-networks/signalsd/app/internal/version.version=${VERSION} -X github.com/information-sharing-networks/signalsd/app/internal/version.buildDate=${BUILD_DATE} -X github.com/information-sharing-networks/signalsd/app/internal/version.gitCommit=${GIT_COMMIT}" \
    -o runtime/signalsd cmd/signalsd/main.go

COPY /app/docs runtime/docs/
COPY /app/assets runtime/assets/

# --- Create a minimal runtime image using scratch ---
FROM scratch

# Create minimal passwd file for non-root user 
RUN echo 'nobody:x:65534:65534:nobody:/:/sbin/nologin' > /etc/passwd && \
    echo 'nobody:x:65534:' > /etc/group

# Copy application files
COPY --from=builder /app/runtime /app/

# Use non-root user (nobody:nobody)
USER nobody:nobody

WORKDIR /app

EXPOSE 8080

ENTRYPOINT ["/app/signalsd"]
