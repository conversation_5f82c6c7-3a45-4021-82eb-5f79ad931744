version: 2
updates:
  # Go modules in main app
  - package-ecosystem: "gomod"
    directory: "/app"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    groups:
      # Group related dependencies together
      google-cloud:
        patterns:
          - "cloud.google.com/*"
          - "google.golang.org/*"
      prometheus:
        patterns:
          - "github.com/prometheus/*"
      testing:
        patterns:
          - "github.com/stretchr/testify"
          - "github.com/testcontainers/*"
    # Only security updates for major version changes
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
    open-pull-requests-limit: 5

  # Docker base images
  - package-ecosystem: "docker"
    directory: "/app"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 3
